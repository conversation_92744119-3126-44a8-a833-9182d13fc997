import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface UserPreferences {
  language: string
  timezone: string
  dateFormat: string
  timeFormat: '12h' | '24h'
  pageSize: number
  autoSave: boolean
  showTips: boolean
  animationsEnabled: boolean
  keyboardShortcuts: boolean
}

export interface SystemSettings {
  version: string
  buildTime: string
  environment: 'development' | 'staging' | 'production'
  apiUrl: string
  features: {
    [key: string]: boolean
  }
}

export const useSettingsStore = defineStore('settings', () => {
  // 用户偏好设置
  const preferences = ref<UserPreferences>({
    language: localStorage.getItem('user-language') || 'zh-CN',
    timezone: localStorage.getItem('user-timezone') || 'Asia/Shanghai',
    dateFormat: localStorage.getItem('date-format') || 'YYYY-MM-DD',
    timeFormat: (localStorage.getItem('time-format') as '12h' | '24h') || '24h',
    pageSize: parseInt(localStorage.getItem('page-size') || '20'),
    autoSave: localStorage.getItem('auto-save') !== 'false',
    showTips: localStorage.getItem('show-tips') !== 'false',
    animationsEnabled: localStorage.getItem('animations-enabled') !== 'false',
    keyboardShortcuts: localStorage.getItem('keyboard-shortcuts') !== 'false'
  })

  // 系统设置
  const systemSettings = ref<SystemSettings>({
    version: '1.0.0',
    buildTime: '2025-01-18',
    environment: 'development',
    apiUrl: import.meta.env.VITE_API_URL || '/api',
    features: {
      darkMode: true,
      notifications: true,
      export: true,
      import: true,
      advancedSearch: true,
      dataVisualization: true,
      realTimeUpdates: false,
      aiAssistant: false
    }
  })

  // 快捷键配置
  const keyboardShortcuts = ref({
    search: 'Ctrl+K',
    newItem: 'Ctrl+N',
    save: 'Ctrl+S',
    refresh: 'F5',
    toggleSidebar: 'Ctrl+B',
    toggleTheme: 'Ctrl+Shift+T',
    showHelp: 'F1'
  })

  // 计算属性
  const isFeatureEnabled = computed(() => (feature: string) => {
    return systemSettings.value.features[feature] || false
  })

  const formattedVersion = computed(() => {
    const { version, buildTime, environment } = systemSettings.value
    return `v${version} (${buildTime}) - ${environment}`
  })

  /**
   * 更新用户偏好
   */
  const updatePreference = <K extends keyof UserPreferences>(
    key: K,
    value: UserPreferences[K]
  ) => {
    preferences.value[key] = value
    localStorage.setItem(
      key === 'language' ? 'user-language' :
      key === 'timezone' ? 'user-timezone' :
      key === 'dateFormat' ? 'date-format' :
      key === 'timeFormat' ? 'time-format' :
      key === 'pageSize' ? 'page-size' :
      key.replace(/([A-Z])/g, '-$1').toLowerCase(),
      value.toString()
    )
  }

  /**
   * 批量更新偏好设置
   */
  const updatePreferences = (newPreferences: Partial<UserPreferences>) => {
    Object.entries(newPreferences).forEach(([key, value]) => {
      updatePreference(key as keyof UserPreferences, value)
    })
  }

  /**
   * 重置用户偏好
   */
  const resetPreferences = () => {
    const defaultPreferences: UserPreferences = {
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      dateFormat: 'YYYY-MM-DD',
      timeFormat: '24h',
      pageSize: 20,
      autoSave: true,
      showTips: true,
      animationsEnabled: true,
      keyboardShortcuts: true
    }

    preferences.value = { ...defaultPreferences }
    
    // 清除localStorage
    Object.keys(defaultPreferences).forEach(key => {
      const storageKey = key === 'language' ? 'user-language' :
                        key === 'timezone' ? 'user-timezone' :
                        key === 'dateFormat' ? 'date-format' :
                        key === 'timeFormat' ? 'time-format' :
                        key === 'pageSize' ? 'page-size' :
                        key.replace(/([A-Z])/g, '-$1').toLowerCase()
      localStorage.removeItem(storageKey)
    })
  }

  /**
   * 切换功能开关
   */
  const toggleFeature = (feature: string) => {
    systemSettings.value.features[feature] = !systemSettings.value.features[feature]
  }

  /**
   * 格式化日期
   */
  const formatDate = (date: Date | string) => {
    const d = typeof date === 'string' ? new Date(date) : date
    const format = preferences.value.dateFormat
    
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    
    return format
      .replace('YYYY', year.toString())
      .replace('MM', month)
      .replace('DD', day)
  }

  /**
   * 格式化时间
   */
  const formatTime = (date: Date | string) => {
    const d = typeof date === 'string' ? new Date(date) : date
    const is24h = preferences.value.timeFormat === '24h'
    
    return d.toLocaleTimeString('zh-CN', {
      hour12: !is24h,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  /**
   * 格式化日期时间
   */
  const formatDateTime = (date: Date | string) => {
    return `${formatDate(date)} ${formatTime(date)}`
  }

  /**
   * 导出设置
   */
  const exportSettings = () => {
    const settings = {
      preferences: preferences.value,
      timestamp: new Date().toISOString(),
      version: systemSettings.value.version
    }
    
    const blob = new Blob([JSON.stringify(settings, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `athena-settings-${formatDate(new Date())}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  /**
   * 导入设置
   */
  const importSettings = (file: File): Promise<boolean> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const settings = JSON.parse(e.target?.result as string)
          
          if (settings.preferences) {
            updatePreferences(settings.preferences)
            resolve(true)
          } else {
            reject(new Error('无效的设置文件格式'))
          }
        } catch (error) {
          reject(new Error('设置文件解析失败'))
        }
      }
      
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsText(file)
    })
  }

  /**
   * 获取系统信息
   */
  const getSystemInfo = () => {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      ...systemSettings.value
    }
  }

  return {
    // 状态
    preferences,
    systemSettings,
    keyboardShortcuts,

    // 计算属性
    isFeatureEnabled,
    formattedVersion,

    // 方法
    updatePreference,
    updatePreferences,
    resetPreferences,
    toggleFeature,
    formatDate,
    formatTime,
    formatDateTime,
    exportSettings,
    importSettings,
    getSystemInfo
  }
})
