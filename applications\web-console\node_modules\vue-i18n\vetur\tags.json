{"i18n-t": {"attributes": ["keypath", "locale", "tag", "plural", "scope"], "description": "This is Translation component that can be used when HTML interpolation is needed.\n\nhttps://vue-i18n.intlify.dev/guide/advanced/component.html#basic-usage"}, "i18n-d": {"attributes": ["value", "format", "locale", "tag", "scope"], "description": "This is Datetime Format component provides a way to use HTML interpolation in pair with number formatting.\n\nhttps://vue-i18n.intlify.dev/guide/essentials/datetime.html#custom-formatting"}, "i18n-n": {"attributes": ["value", "format", "locale", "tag", "scope"], "description": "This is Number Format component provides a way to use HTML interpolation in pair with number formatting.\n\nhttps://vue-i18n.intlify.dev/guide/essentials/number.html#custom-formatting"}}