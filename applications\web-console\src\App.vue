<script setup lang="ts">
import { RouterView } from 'vue-router'
</script>

<template>
  <div id="app">
    <RouterView />
  </div>
</template>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  overflow-x: hidden; /* 防止水平滚动 */
}

#app {
  height: 100vh;
  width: 100vw;
}

/* Element Plus 样式覆盖 */
.el-button {
  font-weight: 500;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-menu {
  border-right: none !important;
}

/* 响应式辅助类 */
.hide-on-mobile {
  display: block;
}

.show-on-mobile {
  display: none;
}

@media (max-width: 768px) {
  .hide-on-mobile {
    display: none;
  }
  
  .show-on-mobile {
    display: block;
  }
  
  /* 移动端表格样式优化 */
  .el-table {
    width: 100%;
    overflow-x: auto;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
