<template>
  <el-container class="main-layout">
    <!-- 顶部导航栏 -->
    <el-header class="main-header glass-effect">
      <div class="header-left">
        <el-button
          type="text"
          class="menu-toggle hover-lift"
          @click="toggleSidebar"
        >
          <el-icon size="20">
            <Menu />
          </el-icon>
        </el-button>
        <h1 class="app-title gradient-text">Athena 智能管理系统</h1>
      </div>

      <div class="header-right">
        <!-- 通知图标 -->
        <el-badge :value="3" class="notification-badge">
          <el-button type="text" class="header-btn">
            <el-icon size="18"><Bell /></el-icon>
          </el-button>
        </el-badge>

        <!-- 全屏切换 -->
        <el-button type="text" class="header-btn" @click="toggleFullscreen">
          <el-icon size="18"><FullScreen /></el-icon>
        </el-button>

        <!-- 用户下拉菜单 -->
        <el-dropdown @command="handleUserCommand" class="user-dropdown-container">
          <span class="user-dropdown hover-lift">
            <el-avatar :size="32" class="user-avatar">
              {{ authStore.username?.charAt(0).toUpperCase() }}
            </el-avatar>
            <span class="username hide-on-mobile">{{ authStore.username }}</span>
            <el-icon class="dropdown-icon hide-on-mobile">
              <ArrowDown />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon> 个人信息
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon> 系统设置
              </el-dropdown-item>
              <el-dropdown-item command="theme">
                <el-icon><Sunny /></el-icon> 主题切换
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon> 退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container class="main-body">
      <!-- 移动端遮罩层 -->
      <div
        v-if="isMobile && !isCollapsed"
        class="sidebar-overlay"
        @click="toggleSidebar"
      ></div>

      <!-- 左侧菜单 -->
      <el-aside :width="sidebarWidth" class="main-sidebar" :class="{ 'mobile-sidebar': isMobile }">
        <div class="sidebar-header">
          <div class="logo-container">
            <el-icon class="logo-icon" size="24">
              <DataBoard />
            </el-icon>
            <span v-if="!isCollapsed" class="logo-text">Athena</span>
          </div>
        </div>

        <el-scrollbar class="sidebar-scrollbar">
          <el-menu
            :default-active="activeMenu"
            class="sidebar-menu"
            :collapse="isCollapsed"
            :unique-opened="true"
            router
          >
            <el-menu-item index="/" class="menu-item-home">
              <el-icon>
                <House />
              </el-icon>
              <template #title>
                <span class="menu-title">首页</span>
              </template>
            </el-menu-item>

            <el-sub-menu index="system" class="menu-group">
              <template #title>
                <el-icon>
                  <Setting />
                </el-icon>
                <span class="menu-title">系统管理</span>
              </template>
              <el-menu-item index="/users">
                <el-icon><User /></el-icon>
                <template #title>用户管理</template>
              </el-menu-item>
              <el-menu-item index="/roles">
                <el-icon><UserFilled /></el-icon>
                <template #title>角色管理</template>
              </el-menu-item>
              <el-menu-item index="/permissions">
                <el-icon><Key /></el-icon>
                <template #title>权限管理</template>
              </el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="data" class="menu-group">
              <template #title>
                <el-icon>
                  <DataBoard />
                </el-icon>
                <span class="menu-title">数据管理</span>
              </template>
              <el-menu-item index="/tasks">
                <el-icon><Document /></el-icon>
                <template #title>任务管理</template>
              </el-menu-item>
              <el-menu-item index="/policies">
                <el-icon><Files /></el-icon>
                <template #title>政策管理</template>
              </el-menu-item>
              <el-menu-item index="/knowledge">
                <el-icon><Reading /></el-icon>
                <template #title>知识库</template>
              </el-menu-item>
            </el-sub-menu>

            <el-menu-item index="/about" class="menu-item-about">
              <el-icon>
                <InfoFilled />
              </el-icon>
              <template #title>
                <span class="menu-title">关于系统</span>
              </template>
            </el-menu-item>
          </el-menu>
        </el-scrollbar>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="main-content">
        <div class="content-wrapper fade-in">
          <router-view v-slot="{ Component }">
            <transition name="page-fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Menu,
  User,
  UserFilled,
  ArrowDown,
  House,
  Setting,
  DataBoard,
  InfoFilled,
  Bell,
  FullScreen,
  Sunny,
  SwitchButton,
  Key,
  Document,
  Files,
  Reading
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 侧边栏状态
const isCollapsed = ref(false)
const isMobile = ref(false)

// 计算属性
const sidebarWidth = computed(() => {
  if (isMobile.value) {
    return isCollapsed.value ? '0px' : '200px'
  }
  return isCollapsed.value ? '64px' : '200px'
})
const activeMenu = computed(() => route.path)

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768
  if (isMobile.value) {
    isCollapsed.value = true
  }
}

// 生命周期钩子
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})

/**
 * 切换侧边栏
 */
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

/**
 * 切换全屏
 */
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    ElMessage.success('已进入全屏模式')
  } else {
    document.exitFullscreen()
    ElMessage.success('已退出全屏模式')
  }
}

/**
 * 处理用户下拉菜单命令
 */
const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中...')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'theme':
      ElMessage.info('主题切换功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        authStore.logout()
        ElMessage.success('已退出登录')
      } catch {
        // 用户取消操作
      }
      break
  }
}
</script>

<style scoped>
.main-layout {
  height: 100vh;
  overflow-x: hidden;
  width: 100%;
  background: var(--bg-secondary);
}

.main-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: var(--shadow-md);
  height: 64px !important;
  position: relative;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.menu-toggle {
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.menu-toggle:hover {
  color: var(--primary-color);
  background: var(--bg-tertiary);
}

.app-title {
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-btn {
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.header-btn:hover {
  color: var(--primary-color);
  background: var(--bg-tertiary);
}

.notification-badge {
  margin-right: 8px;
}

.user-dropdown-container {
  margin-left: 8px;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  gap: 8px;
}

.user-dropdown:hover {
  background: var(--bg-tertiary);
}

.user-avatar {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  font-weight: 600;
}

.username {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
}

.dropdown-icon {
  color: var(--text-tertiary);
  font-size: 12px;
  transition: transform 0.2s ease;
}

.user-dropdown:hover .dropdown-icon {
  transform: rotate(180deg);
}

.main-body {
  height: calc(100vh - 64px);
  overflow: hidden;
  display: flex;
  position: relative;
}

.sidebar-overlay {
  position: fixed;
  top: 64px;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  backdrop-filter: blur(4px);
}

.main-sidebar {
  background: var(--bg-primary);
  border-right: 1px solid var(--border-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1000;
  flex-shrink: 0;
  box-shadow: var(--shadow-md);
}

.main-sidebar.mobile-sidebar {
  position: fixed;
  top: 64px;
  left: 0;
  height: calc(100vh - 64px);
  z-index: 1001;
}

.sidebar-header {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  color: var(--primary-color);
}

.logo-text {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
}

.sidebar-scrollbar {
  height: calc(100% - 60px);
}

.sidebar-menu {
  border-right: none;
  background: transparent;
  padding: 8px;
}

.menu-item-home,
.menu-item-about {
  margin: 4px 0;
}

.menu-group {
  margin: 8px 0;
}

.menu-title {
  font-weight: 500;
}

.main-content {
  background: var(--bg-secondary);
  padding: 0 !important;
  overflow-y: auto;
  flex: 1;
  width: 100%;
}

.content-wrapper {
  padding: 24px;
  min-height: 100%;
}

/* 页面切换动画 */
.page-fade-enter-active,
.page-fade-leave-active {
  transition: all 0.3s ease;
}

.page-fade-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-fade-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-wrapper {
    padding: 20px;
  }

  .app-title {
    font-size: 18px;
  }
}

@media (max-width: 768px) {
  .main-header {
    padding: 0 16px;
    height: 56px !important;
  }

  .main-body {
    height: calc(100vh - 56px);
  }

  .sidebar-overlay {
    top: 56px;
  }

  .main-sidebar.mobile-sidebar {
    top: 56px;
    height: calc(100vh - 56px);
  }

  .app-title {
    font-size: 16px;
  }

  .content-wrapper {
    padding: 16px;
  }

  .header-right {
    gap: 8px;
  }

  .notification-badge {
    margin-right: 4px;
  }
}

@media (max-width: 576px) {
  .main-header {
    padding: 0 12px;
    height: 52px !important;
  }

  .main-body {
    height: calc(100vh - 52px);
  }

  .sidebar-overlay {
    top: 52px;
  }

  .main-sidebar.mobile-sidebar {
    top: 52px;
    height: calc(100vh - 52px);
  }

  .app-title {
    font-size: 14px;
    max-width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .content-wrapper {
    padding: 12px;
  }

  .header-right {
    gap: 4px;
  }

  .user-dropdown {
    padding: 6px 8px;
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  .main-header {
    background: rgba(31, 41, 55, 0.95);
    border-bottom-color: rgba(75, 85, 99, 0.3);
  }

  .main-sidebar {
    background: rgb(31, 41, 55);
    border-right-color: rgba(75, 85, 99, 0.3);
  }

  .sidebar-header {
    background: linear-gradient(135deg, rgb(31, 41, 55) 0%, rgb(55, 65, 81) 100%);
    border-bottom-color: rgba(75, 85, 99, 0.3);
  }
}
</style>
