<template>
  <el-container class="main-layout">
    <!-- 顶部导航栏 -->
    <el-header class="main-header">
      <div class="header-left">
        <el-button
          type="text"
          class="menu-toggle"
          @click="toggleSidebar"
        >
          <el-icon size="20">
            <Menu />
          </el-icon>
        </el-button>
        <h1 class="app-title">Athena 管理系统</h1>
      </div>
      
      <div class="header-right">
        <el-dropdown @command="handleUserCommand">
          <span class="user-dropdown">
            <el-icon class="user-icon">
              <User />
            </el-icon>
            <span class="username">{{ authStore.username }}</span>
            <el-icon class="dropdown-icon">
              <ArrowDown />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人信息</el-dropdown-item>
              <el-dropdown-item command="settings">系统设置</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container class="main-body">
      <!-- 左侧菜单 -->
      <el-aside :width="sidebarWidth" class="main-sidebar">
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          :collapse="isCollapsed"
          :unique-opened="true"
          router
        >
          <el-menu-item index="/">
            <el-icon>
              <House />
            </el-icon>
            <template #title>首页</template>
          </el-menu-item>
          
          <el-sub-menu index="system">
            <template #title>
              <el-icon>
                <Setting />
              </el-icon>
              <span>系统管理</span>
            </template>
            <el-menu-item index="/users">用户管理</el-menu-item>
            <el-menu-item index="/roles">角色管理</el-menu-item>
            <el-menu-item index="/permissions">权限管理</el-menu-item>
          </el-sub-menu>
          
          <el-sub-menu index="data">
            <template #title>
              <el-icon>
                <DataBoard />
              </el-icon>
              <span>数据管理</span>
            </template>
            <el-menu-item index="/tasks">任务管理</el-menu-item>
            <el-menu-item index="/policies">政策管理</el-menu-item>
            <el-menu-item index="/knowledge">知识库</el-menu-item>
          </el-sub-menu>
          
          <el-menu-item index="/about">
            <el-icon>
              <InfoFilled />
            </el-icon>
            <template #title>关于系统</template>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="main-content">
          <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Menu,
  User,
  ArrowDown,
  House,
  Setting,
  DataBoard,
  InfoFilled
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 侧边栏状态
const isCollapsed = ref(false)

// 计算属性
const sidebarWidth = computed(() => isCollapsed.value ? '64px' : '200px')
const activeMenu = computed(() => route.path)

/**
 * 切换侧边栏
 */
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

/**
 * 处理用户下拉菜单命令
 */
const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中...')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        
        authStore.logout()
        ElMessage.success('已退出登录')
      } catch {
        // 用户取消操作
      }
      break
  }
}
</script>

<style scoped>
.main-layout {
  height: 100vh;
  overflow-x: hidden; /* 防止水平滚动条 */
  width: 100%;
}

.main-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  height: 60px !important; /* 固定头部高度 */
}

.header-left {
  display: flex;
  align-items: center;
}

.menu-toggle {
  margin-right: 16px;
  color: #606266;
}

.app-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown:hover {
  background-color: #f5f7fa;
}

.user-icon {
  margin-right: 8px;
  color: #606266;
}

.username {
  color: #303133;
  font-size: 14px;
  margin-right: 4px;
}

.dropdown-icon {
  color: #909399;
  font-size: 12px;
}

.main-body {
  height: calc(100vh - 60px);
  overflow: hidden; /* 防止嵌套滚动 */
  display: flex; /* 使用flex布局 */
}

.main-sidebar {
  background: #fff;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s;
  position: relative; /* 定位上下文 */
  z-index: 10; /* 提高层级 */
  flex-shrink: 0; /* 防止侧边栏被压缩 */
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.main-content {
  background: #f0f2f5;
  padding: 20px !important; /* 直接设置内边距 */
  overflow-y: auto;
  flex: 1;
  width: 100%; /* 占用所有可用空间 */
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-content {
    padding: 16px !important;
  }
}

@media (max-width: 768px) {
  .main-header {
    padding: 0 16px;
  }
  
  .app-title {
    font-size: 16px;
  }
  
  .main-content {
    padding: 16px 12px !important;
  }
  
  /* 在小屏幕上默认收起侧边栏 */
  .main-sidebar {
    position: fixed;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  }
}

@media (max-width: 576px) {
  .main-header {
    padding: 0 12px;
  }
  
  .app-title {
    font-size: 15px;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .username {
    display: none;
  }
  
  .main-content {
    padding: 12px 8px !important;
  }
}
</style>
