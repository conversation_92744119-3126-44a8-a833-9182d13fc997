<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import {
  Plus,
  Search,
  User,
  Document,
  Files,
  CircleCheck,
  Calendar,
  Sunny,
  DataBoard,
  TrendCharts
} from '@element-plus/icons-vue'

const authStore = useAuthStore()
const router = useRouter()

// 系统统计数据
const stats = ref({
  totalUsers: 1234,
  totalTasks: 567,
  totalPolicies: 89,
  systemStatus: '正常'
})

// 动画状态
const isCountingUsers = ref(false)
const isCountingTasks = ref(false)
const isCountingPolicies = ref(false)

// 显示的数字（用于动画）
const displayUsers = ref(0)
const displayTasks = ref(0)
const displayPolicies = ref(0)

// 最近活动数据
const recentActivities = ref([
  { id: 1, action: '用户登录', user: 'admin', time: '2025-01-17 10:30:00' },
  { id: 2, action: '创建任务', user: 'user1', time: '2025-01-17 10:25:00' },
  { id: 3, action: '更新政策', user: 'admin', time: '2025-01-17 10:20:00' },
  { id: 4, action: '查询知识库', user: 'user2', time: '2025-01-17 10:15:00' },
])

// 获取问候语
const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 6) return '夜深了'
  if (hour < 9) return '早上好'
  if (hour < 12) return '上午好'
  if (hour < 14) return '中午好'
  if (hour < 18) return '下午好'
  if (hour < 22) return '晚上好'
  return '夜深了'
}

// 获取当前日期
const getCurrentDate = () => {
  const now = new Date()
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  }
  return now.toLocaleDateString('zh-CN', options)
}

// 数字动画函数
const animateNumber = (target: number, displayRef: any, isCountingRef: any) => {
  isCountingRef.value = true
  const duration = 2000
  const start = 0
  const startTime = Date.now()

  const animate = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / duration, 1)

    // 使用缓动函数
    const easeOutQuart = 1 - Math.pow(1 - progress, 4)
    displayRef.value = Math.floor(start + (target - start) * easeOutQuart)

    if (progress < 1) {
      requestAnimationFrame(animate)
    } else {
      isCountingRef.value = false
    }
  }

  requestAnimationFrame(animate)
}

// 处理统计卡片点击
const handleStatClick = (type: string) => {
  switch (type) {
    case 'users':
      router.push('/users')
      break
    case 'tasks':
      router.push('/tasks')
      break
    case 'policies':
      router.push('/policies')
      break
    case 'system':
      ElMessage.info('系统状态详情功能开发中...')
      break
  }
}

// 处理欢迎区域按钮点击
const handleCreateTask = () => {
  router.push('/tasks')
  ElMessage.success('跳转到任务管理页面')
}

const handleSearchData = () => {
  ElMessage.info('数据查询功能开发中...')
}

const handleViewReports = () => {
  ElMessage.info('报告查看功能开发中...')
}

onMounted(() => {
  console.log('首页加载完成，当前用户:', authStore.username)

  // 延迟启动数字动画
  setTimeout(() => {
    animateNumber(stats.value.totalUsers, displayUsers, isCountingUsers)
  }, 500)

  setTimeout(() => {
    animateNumber(stats.value.totalTasks, displayTasks, isCountingTasks)
  }, 700)

  setTimeout(() => {
    animateNumber(stats.value.totalPolicies, displayPolicies, isCountingPolicies)
  }, 900)
})
</script>

<template>
  <div class="home-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section fade-in">
      <el-card class="welcome-card hover-lift">
        <div class="welcome-header">
          <div class="user-info">
            <el-avatar :size="64" class="user-avatar">
              {{ authStore.username?.charAt(0).toUpperCase() }}
            </el-avatar>
            <div class="user-details">
              <h2 class="welcome-title">
                <span class="greeting">{{ getGreeting() }}，</span>
                <span class="username-highlight">{{ authStore.username }}！</span>
              </h2>
              <p class="welcome-subtitle">
                <el-icon class="date-icon"><Calendar /></el-icon>
                今天是 {{ getCurrentDate() }}，祝您工作愉快！
              </p>
              <div class="user-status">
                <div class="online-indicator">
                  <div class="status-dot"></div>
                  <span>在线</span>
                </div>
                <div class="weather-info">
                  <el-icon class="weather-icon"><Sunny /></el-icon>
                  <span>晴朗 22°C</span>
                </div>
              </div>
            </div>
          </div>
          <div class="quick-stats">
            <div class="quick-stat-item">
              <div class="stat-value">{{ stats.totalTasks }}</div>
              <div class="stat-label">待处理任务</div>
            </div>
            <div class="quick-stat-item">
              <div class="stat-value">{{ stats.totalPolicies }}</div>
              <div class="stat-label">新政策</div>
            </div>
          </div>
        </div>

        <div class="welcome-actions">
          <el-button type="primary" size="large" class="action-btn primary-action" @click="handleCreateTask">
            <el-icon><Plus /></el-icon>
            创建新任务
          </el-button>
          <el-button size="large" class="action-btn secondary-action" @click="handleSearchData">
            <el-icon><Search /></el-icon>
            查询数据
          </el-button>
          <el-button size="large" class="action-btn secondary-action" @click="handleViewReports">
            <el-icon><DataBoard /></el-icon>
            查看报告
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section slide-in-right">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card hover-lift" @click="handleStatClick('users')">
            <div class="stat-content">
              <div class="stat-icon user-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number" :class="{ 'counting': isCountingUsers }">
                  {{ displayUsers }}
                </div>
                <div class="stat-label">总用户数</div>
                <div class="stat-trend">
                  <el-icon class="trend-icon up"><TrendCharts /></el-icon>
                  <span class="trend-text">+12%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card hover-lift" @click="handleStatClick('tasks')">
            <div class="stat-content">
              <div class="stat-icon task-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number" :class="{ 'counting': isCountingTasks }">
                  {{ displayTasks }}
                </div>
                <div class="stat-label">总任务数</div>
                <div class="stat-trend">
                  <el-icon class="trend-icon up"><TrendCharts /></el-icon>
                  <span class="trend-text">+8%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card hover-lift" @click="handleStatClick('policies')">
            <div class="stat-content">
              <div class="stat-icon policy-icon">
                <el-icon><Files /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number" :class="{ 'counting': isCountingPolicies }">
                  {{ displayPolicies }}
                </div>
                <div class="stat-label">政策数量</div>
                <div class="stat-trend">
                  <el-icon class="trend-icon up"><TrendCharts /></el-icon>
                  <span class="trend-text">+5%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card hover-lift system-status-card" @click="handleStatClick('system')">
            <div class="stat-content">
              <div class="stat-icon status-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number status-text">{{ stats.systemStatus }}</div>
                <div class="stat-label">系统状态</div>
                <div class="stat-trend">
                  <div class="status-indicator">
                    <div class="status-dot pulse"></div>
                    <span class="status-uptime">运行时间: 99.9%</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近活动 -->
    <div class="activity-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>最近活动</span>
            <el-button type="text">查看全部</el-button>
          </div>
        </template>

        <el-table :data="recentActivities" style="width: 100%">
          <el-table-column prop="action" label="操作" width="120" />
          <el-table-column prop="user" label="用户" width="100" />
          <el-table-column prop="time" label="时间" />
          <el-table-column label="操作" width="100">
            <template #default>
              <el-button type="text" size="small">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  padding: 0 15px;
  width: 100%; /* 使用全宽 */
  max-width: 100%; /* 移除最大宽度限制 */
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 24px;
  width: 100%; /* 确保欢迎区域使用全宽 */
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.welcome-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.welcome-card :deep(.el-card__body) {
  padding: 32px;
  position: relative;
  z-index: 1;
}

.welcome-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.user-info {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1;
}

.user-avatar {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  color: white;
  font-weight: 700;
  font-size: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  flex-shrink: 0;
}

.user-details {
  flex: 1;
}

.welcome-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
}

.greeting {
  opacity: 0.9;
}

.username-highlight {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  margin: 0 0 12px 0;
  opacity: 0.9;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-icon {
  font-size: 16px;
}

.user-status {
  display: flex;
  align-items: center;
  gap: 16px;
}

.online-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  opacity: 0.9;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.weather-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  opacity: 0.8;
}

.weather-icon {
  font-size: 14px;
  color: #ffd700;
}

.quick-stats {
  display: flex;
  gap: 24px;
}

.quick-stat-item {
  text-align: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

.welcome-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-start;
}

.action-btn {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  font-weight: 500;
  transition: all 0.3s ease;
  flex: 0 0 auto;
}

.primary-action {
  background: rgba(255, 255, 255, 0.2) !important;
}

.secondary-action {
  background: rgba(255, 255, 255, 0.1) !important;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.primary-action:hover {
  background: rgba(255, 255, 255, 0.3) !important;
}

.secondary-action:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}

.stats-section {
  margin-bottom: 24px;
  width: 100%; /* 确保统计区域使用全宽 */
}

.stat-card {
  height: 120px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.stat-card:hover::before {
  transform: translateX(100%);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 16px;
  position: relative;
  z-index: 1;
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
  position: relative;
  overflow: hidden;
}

.stat-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.stat-card:hover .stat-icon::before {
  transform: translateX(100%);
}

.user-icon {
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
}

.task-icon {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}

.policy-icon {
  background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%);
}

.status-icon {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 6px;
  transition: all 0.3s ease;
}

.stat-number.counting {
  color: var(--primary-color);
  transform: scale(1.1);
}

.status-text {
  font-size: 20px !important;
  color: var(--success-color) !important;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.trend-icon {
  font-size: 14px;
}

.trend-icon.up {
  color: var(--success-color);
}

.trend-text {
  color: var(--success-color);
  font-weight: 500;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-uptime {
  color: var(--text-tertiary);
}

.system-status-card .stat-content {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(52, 211, 153, 0.05) 100%);
  border-radius: var(--radius-lg);
}

.activity-section {
  margin-bottom: 24px;
  width: 100%; /* 确保活动区域使用全宽 */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .home-container {
    max-width: 100%;
  }

  .welcome-title {
    font-size: 24px;
  }
}

@media (max-width: 992px) {
  .welcome-title {
    font-size: 22px;
  }

  .stat-number {
    font-size: 24px;
  }
}

@media (max-width: 768px) {
  .welcome-card :deep(.el-card__body) {
    padding: 24px 20px;
  }

  .welcome-header {
    flex-direction: column;
    gap: 20px;
    align-items: center;
    text-align: center;
  }

  .user-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 12px;
  }

  .user-status {
    justify-content: center;
  }

  .quick-stats {
    gap: 16px;
  }

  .welcome-actions {
    justify-content: center;
    flex-wrap: wrap;
    gap: 12px;
  }

  .welcome-title {
    font-size: 20px;
  }

  .welcome-subtitle {
    justify-content: center;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
    padding: 20px 16px;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .stat-number {
    font-size: 24px;
  }

  .stat-trend {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .welcome-card :deep(.el-card__body) {
    padding: 20px 16px;
  }

  .welcome-title {
    font-size: 18px;
  }

  .welcome-subtitle {
    font-size: 13px;
  }

  .quick-stats {
    flex-direction: column;
    gap: 12px;
  }

  .quick-stat-item {
    padding: 8px 12px;
  }

  .stat-value {
    font-size: 18px;
  }

  .welcome-actions {
    width: 100%;
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }

  .stat-card {
    height: 100px;
  }

  .stat-content {
    padding: 16px 12px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .stat-number {
    font-size: 20px;
  }

  .activity-section :deep(.el-table) {
    font-size: 12px;
  }
}

/* 动画增强 */
@keyframes countUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.stat-number.counting {
  animation: countUp 0.6s ease-out;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .welcome-card {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  }

  .stat-card {
    background: rgb(31, 41, 55);
    border-color: rgba(75, 85, 99, 0.3);
  }

  .stat-card:hover {
    background: rgb(55, 65, 81);
  }
}
</style>
